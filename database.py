import sqlite3
from datetime import datetime
import random
import string

def init_db():
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()

    # Check if users table exists
    c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
    table_exists = c.fetchone()

    # Only create tables if they don't exist
    if not table_exists:
        # Create users table with unique_id
        c.execute('''
            CREATE TABLE users
            (id INTEGER PRIMARY KEY AUTOINCREMENT,
             name TEXT NOT NULL,
             unique_id TEXT UNIQUE NOT NULL,
             created_at TIMESTAMP,
             profile_pic TEXT,
             birthdate TEXT,
             hobbies TEXT)
        ''')

        # Create conversations table with is_saved column and user_id
        c.execute('''
            CREATE TABLE conversations
            (id INTEGER PRIMARY KEY AUTOINCREMENT,
             title TEXT,
             created_at TIMESTAMP,
             last_updated TIMESTAMP,
             is_saved BOOLEAN DEFAULT 0,
             user_id INTEGER,
             FOREIGN KEY (user_id) REFERENCES users (id))
        ''')

        # Create messages table
        c.execute('''
            CREATE TABLE messages
            (id INTEGER PRIMARY KEY AUTOINCREMENT,
             conversation_id INTEGER,
             content TEXT,
             is_user BOOLEAN,
             timestamp TIMESTAMP,
             FOREIGN KEY (conversation_id) REFERENCES conversations (id))
        ''')

        # Create threads table to store AI model conversation threads
        c.execute('''
            CREATE TABLE threads
            (id INTEGER PRIMARY KEY AUTOINCREMENT,
             conversation_id INTEGER,
             thread_data TEXT,
             updated_at TIMESTAMP,
             FOREIGN KEY (conversation_id) REFERENCES conversations (id))
        ''')

        # Create friends table
        c.execute('''
            CREATE TABLE friends
            (id INTEGER PRIMARY KEY AUTOINCREMENT,
             user_id INTEGER NOT NULL,
             friend_id INTEGER NOT NULL,
             status TEXT NOT NULL, -- 'pending', 'accepted', 'declined', 'blocked'
             created_at TIMESTAMP,
             FOREIGN KEY (user_id) REFERENCES users (id),
             FOREIGN KEY (friend_id) REFERENCES users (id),
             UNIQUE(user_id, friend_id))
        ''')

        # Create private_chats table
        c.execute('''
            CREATE TABLE private_chats
            (id INTEGER PRIMARY KEY AUTOINCREMENT,
             user1_id INTEGER NOT NULL,
             user2_id INTEGER NOT NULL,
             created_at TIMESTAMP,
             last_updated TIMESTAMP,
             FOREIGN KEY (user1_id) REFERENCES users (id),
             FOREIGN KEY (user2_id) REFERENCES users (id),
             UNIQUE(user1_id, user2_id))
        ''')

        # Create private_messages table
        c.execute('''
            CREATE TABLE private_messages
            (id INTEGER PRIMARY KEY AUTOINCREMENT,
             private_chat_id INTEGER NOT NULL,
             sender_id INTEGER NOT NULL,
             content TEXT,
             timestamp TIMESTAMP,
             FOREIGN KEY (private_chat_id) REFERENCES private_chats (id),
             FOREIGN KEY (sender_id) REFERENCES users (id))
        ''')

        # Create group_chats table
        c.execute('''
            CREATE TABLE group_chats
            (id INTEGER PRIMARY KEY AUTOINCREMENT,
             name TEXT NOT NULL,
             created_by_user_id INTEGER NOT NULL,
             created_at TIMESTAMP,
             last_updated TIMESTAMP,
             FOREIGN KEY (created_by_user_id) REFERENCES users (id))
        ''')

        # Create group_chat_members table
        c.execute('''
            CREATE TABLE group_chat_members
            (group_chat_id INTEGER NOT NULL,
             user_id INTEGER NOT NULL,
             joined_at TIMESTAMP,
             PRIMARY KEY (group_chat_id, user_id),
             FOREIGN KEY (group_chat_id) REFERENCES group_chats (id),
             FOREIGN KEY (user_id) REFERENCES users (id))
        ''')

        # Create group_messages table
        c.execute('''
            CREATE TABLE group_messages
            (id INTEGER PRIMARY KEY AUTOINCREMENT,
             group_chat_id INTEGER NOT NULL,
             sender_id INTEGER NOT NULL,
             content TEXT,
             timestamp TIMESTAMP,
             is_ai_message BOOLEAN DEFAULT 0,
             FOREIGN KEY (group_chat_id) REFERENCES group_chats (id),
             FOREIGN KEY (sender_id) REFERENCES users (id))
        ''')

        conn.commit()
        print("Database tables created successfully")
    else:
        print("Database tables already exist")
    conn.close()

def create_conversation(title):
    # This function is kept for backward compatibility
    # but should not be used directly anymore
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    now = datetime.now()
    c.execute('INSERT INTO conversations (title, created_at, last_updated) VALUES (?, ?, ?)',
              (title, now, now))
    conversation_id = c.lastrowid
    conn.commit()
    conn.close()
    return conversation_id

def save_message(conversation_id, content, is_user):
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    now = datetime.now()
    c.execute('INSERT INTO messages (conversation_id, content, is_user, timestamp) VALUES (?, ?, ?, ?)',
              (conversation_id, content, is_user, now))
    c.execute('UPDATE conversations SET last_updated = ? WHERE id = ?',
              (now, conversation_id))
    conn.commit()
    conn.close()

def get_conversations():
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    # First get saved conversations, then others, both ordered by last_updated
    c.execute('''
        SELECT id, title, created_at, is_saved
        FROM conversations
        ORDER BY is_saved DESC, last_updated DESC
    ''')
    conversations = c.fetchall()
    conn.close()
    return conversations

def get_conversation_messages(conversation_id):
    conn = sqlite3.connect('chats.db')
    # Set timeout to avoid database locks
    conn.execute('PRAGMA busy_timeout = 5000')
    # Use WAL mode for better concurrency
    conn.execute('PRAGMA journal_mode = WAL')
    c = conn.cursor()

    try:
        # Add index hint for better performance
        c.execute('''
            SELECT content, is_user, timestamp
            FROM messages
            WHERE conversation_id = ?
            ORDER BY timestamp
        ''', (conversation_id,))
        messages = c.fetchall()
        return messages
    except Exception as e:
        print(f"Error getting conversation messages: {str(e)}")
        return []
    finally:
        conn.close()

def update_conversation_title(conversation_id, new_title):
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('UPDATE conversations SET title = ? WHERE id = ?',
              (new_title, conversation_id))
    conn.commit()
    conn.close()

def delete_conversation(conversation_id):
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    # Delete threads and messages first due to foreign key constraint
    c.execute('DELETE FROM threads WHERE conversation_id = ?', (conversation_id,))
    c.execute('DELETE FROM messages WHERE conversation_id = ?', (conversation_id,))
    c.execute('DELETE FROM conversations WHERE id = ?', (conversation_id,))
    conn.commit()
    conn.close()

def toggle_save_conversation(conversation_id):
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('''
        UPDATE conversations
        SET is_saved = CASE WHEN is_saved = 0 THEN 1 ELSE 0 END
        WHERE id = ?
    ''', (conversation_id,))
    conn.commit()
    conn.close()

# User-related functions
def generate_unique_id_suffix():
    """Generates a 4-digit random number suffix."""
    return ''.join(random.choices(string.digits, k=4))

def register_user(name):
    """Register a new user with just their username as unique ID."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    now = datetime.now()

    # Use the username directly as unique_id (no suffix)
    unique_id = name.lower().replace(" ", "")

    # Check if username already exists
    if get_user_by_unique_id(unique_id):
        conn.close()
        return None, None  # Username already taken

    try:
        # Check if the table has the new columns
        c.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in c.fetchall()]

        if 'name' in columns and 'unique_id' in columns:
            # New schema
            c.execute('''
                INSERT INTO users
                (name, unique_id, created_at, profile_pic, birthdate, hobbies)
                VALUES (?, ?, ?, NULL, NULL, NULL)
            ''', (name, unique_id, now))
        else:
            # Old schema - use username and email fields
            c.execute('''
                INSERT INTO users
                (username, email, created_at, profile_pic, birthdate, hobbies)
                VALUES (?, ?, ?, NULL, NULL, NULL)
            ''', (name, unique_id, now))

        conn.commit()
        user_id = c.lastrowid
        conn.close()
        return user_id, unique_id
    except sqlite3.IntegrityError as e:
        print(f"Registration error: {e}")
        conn.close()
        return None, None

def get_user_by_unique_id(unique_id):
    """Get user by unique ID."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()

    # Check if the table has the new columns
    c.execute("PRAGMA table_info(users)")
    columns = [column[1] for column in c.fetchall()]

    if 'name' in columns and 'unique_id' in columns:
        # New schema
        c.execute('SELECT id, name, unique_id FROM users WHERE unique_id = ?', (unique_id,))
    else:
        # Old schema - use username and email fields
        c.execute('SELECT id, username, email FROM users WHERE email = ?', (unique_id,))

    user = c.fetchone()
    conn.close()
    return user

def verify_user_by_unique_id(unique_id):
    """Verify user by unique ID (no password)."""
    user = get_user_by_unique_id(unique_id)
    if user:
        return user[0] # Return user ID
    return None

def get_user_by_id(user_id):
    """Get user by ID."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('SELECT id, name, unique_id FROM users WHERE id = ?', (user_id,))
    user = c.fetchone()
    conn.close()
    return user

def create_conversation_for_user(title, user_id):
    """Create a conversation for a specific user."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    now = datetime.now()
    c.execute('INSERT INTO conversations (title, created_at, last_updated, user_id) VALUES (?, ?, ?, ?)',
              (title, now, now, user_id))
    conversation_id = c.lastrowid
    conn.commit()
    conn.close()
    return conversation_id

def get_user_conversations(user_id):
    """Get all conversations for a user."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('''
        SELECT id, title, created_at, is_saved
        FROM conversations
        WHERE user_id = ?
        ORDER BY is_saved DESC, last_updated DESC
    ''', (user_id,))
    conversations = c.fetchall()
    conn.close()
    return conversations

def get_message_count(conversation_id):
    """Get the number of messages in a conversation."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('SELECT COUNT(*) FROM messages WHERE conversation_id = ?', (conversation_id,))
    count = c.fetchone()[0]
    conn.close()
    return count

def get_conversation_summary(conversation_id):
    """Get a summary of the conversation for title generation."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    # Get the last 10 messages to summarize the conversation
    c.execute('''
        SELECT content, is_user FROM messages
        WHERE conversation_id = ?
        ORDER BY timestamp DESC LIMIT 10
    ''', (conversation_id,))
    messages = c.fetchall()
    conn.close()

    # Format the messages for the AI to process
    formatted_messages = []
    for content, is_user in reversed(messages):  # Reverse to get chronological order
        role = "User" if is_user else "AI"
        # Truncate long messages
        if len(content) > 100:
            content = content[:100] + "..."
        formatted_messages.append(f"{role}: {content}")

    return "\n".join(formatted_messages)

def update_user_profile(user_id, profile_data):
    """Update user profile information."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()

    # Build the update query dynamically based on provided fields
    fields = []
    values = []

    for field, value in profile_data.items():
        if field in ['name', 'profile_pic', 'birthdate', 'hobbies']:
            fields.append(f"{field} = ?")
            values.append(value)

    if not fields:
        conn.close()
        return False

    query = f"UPDATE users SET {', '.join(fields)} WHERE id = ?"
    values.append(user_id)

    c.execute(query, values)
    conn.commit()
    conn.close()
    return True

def get_user_profile(user_id):
    """Get complete user profile information."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('''
        SELECT id, name, unique_id, created_at, profile_pic, birthdate, hobbies
        FROM users
        WHERE id = ?
    ''', (user_id,))
    user = c.fetchone()
    conn.close()

    if not user:
        return None

    # Format as dictionary
    user_dict = {
        'id': user[0],
        'name': user[1],
        'unique_id': user[2],
        'created_at': user[3],
        'profile_pic': user[4],
        'birthdate': user[5],
        'hobbies': user[6]
    }

    return user_dict

# Friend-related functions
def send_friend_request(sender_id, receiver_unique_id):
    """Send a friend request."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    now = datetime.now()

    receiver = get_user_by_unique_id(receiver_unique_id)
    if not receiver:
        conn.close()
        return False, "Receiver not found."

    receiver_id = receiver[0]

    if sender_id == receiver_id:
        conn.close()
        return False, "Cannot send friend request to yourself."

    # Check if a request already exists or they are already friends
    c.execute('''
        SELECT status FROM friends
        WHERE (user_id = ? AND friend_id = ?) OR (user_id = ? AND friend_id = ?)
    ''', (sender_id, receiver_id, receiver_id, sender_id))
    existing_status = c.fetchone()

    if existing_status:
        if existing_status[0] == 'pending':
            conn.close()
            return False, "Friend request already pending."
        elif existing_status[0] == 'accepted':
            conn.close()
            return False, "Already friends."
        elif existing_status[0] == 'blocked':
            conn.close()
            return False, "User is blocked or has blocked you."

    try:
        c.execute('''
            INSERT INTO friends
            (user_id, friend_id, status, created_at)
            VALUES (?, ?, ?, ?)
        ''', (sender_id, receiver_id, 'pending', now))
        conn.commit()
        conn.close()
        return True, "Friend request sent."
    except sqlite3.IntegrityError as e:
        print(f"Error sending friend request: {e}")
        conn.close()
        return False, "Failed to send friend request due to database error."

def accept_friend_request(request_id):
    """Accept a pending friend request."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    now = datetime.now()

    c.execute('SELECT user_id, friend_id FROM friends WHERE id = ? AND status = ?', (request_id, 'pending'))
    request_info = c.fetchone()

    if not request_info:
        conn.close()
        return False, "Friend request not found or not pending."

    sender_id, receiver_id = request_info

    try:
        # Update the existing request to 'accepted'
        c.execute('UPDATE friends SET status = ? WHERE id = ?', ('accepted', request_id))

        # Create a reciprocal entry for the other user
        c.execute('''
            INSERT INTO friends
            (user_id, friend_id, status, created_at)
            VALUES (?, ?, ?, ?)
        ''', (receiver_id, sender_id, 'accepted', now))

        conn.commit()
        conn.close()
        return True, "Friend request accepted."
    except sqlite3.IntegrityError as e:
        print(f"Error accepting friend request: {e}")
        conn.close()
        return False, "Failed to accept friend request due to database error."

def decline_friend_request(request_id):
    """Decline a pending friend request."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()

    c.execute('SELECT id FROM friends WHERE id = ? AND status = ?', (request_id, 'pending'))
    request_exists = c.fetchone()

    if not request_exists:
        conn.close()
        return False, "Friend request not found or not pending."

    c.execute('UPDATE friends SET status = ? WHERE id = ?', ('declined', request_id))
    conn.commit()
    conn.close()
    return True, "Friend request declined."

def get_pending_friend_requests(user_id):
    """Get all pending friend requests for a user (where user_id is the receiver)."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('''
        SELECT f.id, u.id, u.name, u.unique_id, f.created_at
        FROM friends f
        JOIN users u ON f.user_id = u.id
        WHERE f.friend_id = ? AND f.status = ?
    ''', (user_id, 'pending'))
    requests = c.fetchall()
    conn.close()
    return requests

def get_friends_list(user_id):
    """Get the list of accepted friends for a user."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('''
        SELECT u.id, u.name, u.unique_id
        FROM friends f
        JOIN users u ON f.friend_id = u.id
        WHERE f.user_id = ? AND f.status = ?
    ''', (user_id, 'accepted'))
    friends = c.fetchall()
    conn.close()
    return friends

def get_friendship_status(user_id, friend_id):
    """Get the friendship status between two users."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('''
        SELECT status FROM friends
        WHERE (user_id = ? AND friend_id = ?) OR (user_id = ? AND friend_id = ?)
    ''', (user_id, friend_id, friend_id, user_id))
    status = c.fetchone()
    conn.close()
    return status[0] if status else None

def get_user_by_name_and_suffix(name, suffix):
    """Get user by name and unique ID suffix."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    unique_id_pattern = f"{name.lower().replace(' ', '')}#{suffix}"
    c.execute('SELECT id, name, unique_id FROM users WHERE unique_id = ?', (unique_id_pattern,))
    user = c.fetchone()
    conn.close()
    return user

# Private Chat functions
def get_private_chat_id(user1_id, user2_id):
    """Get the ID of an existing private chat between two users."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('''
        SELECT id FROM private_chats
        WHERE (user1_id = ? AND user2_id = ?) OR (user1_id = ? AND user2_id = ?)
    ''', (user1_id, user2_id, user2_id, user1_id))
    chat_id = c.fetchone()
    conn.close()
    return chat_id[0] if chat_id else None

def create_private_chat(user1_id, user2_id):
    """Create a new private chat between two users."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    now = datetime.now()

    # Ensure the chat doesn't already exist
    existing_chat_id = get_private_chat_id(user1_id, user2_id)
    if existing_chat_id:
        conn.close()
        return existing_chat_id

    try:
        c.execute('''
            INSERT INTO private_chats
            (user1_id, user2_id, created_at, last_updated)
            VALUES (?, ?, ?, ?)
        ''', (user1_id, user2_id, now, now))
        chat_id = c.lastrowid
        conn.commit()
        conn.close()
        return chat_id
    except sqlite3.IntegrityError as e:
        print(f"Error creating private chat: {e}")
        conn.close()
        return None

def save_private_message(private_chat_id, sender_id, content):
    """Save a message in a private chat."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    now = datetime.now()
    c.execute('INSERT INTO private_messages (private_chat_id, sender_id, content, timestamp) VALUES (?, ?, ?, ?)',
              (private_chat_id, sender_id, content, now))
    c.execute('UPDATE private_chats SET last_updated = ? WHERE id = ?',
              (now, private_chat_id))
    conn.commit()
    conn.close()

def get_private_messages(private_chat_id):
    """Get all messages from a private chat."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('''
        SELECT sender_id, content, timestamp FROM private_messages
        WHERE private_chat_id = ? ORDER BY timestamp
    ''', (private_chat_id,))
    messages = c.fetchall()
    conn.close()
    return messages

def get_user_private_chats(user_id):
    """Get all private chats for a user."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('''
        SELECT pc.id,
               CASE
                   WHEN pc.user1_id = ? THEN u2.name || '#' || SUBSTR(u2.unique_id, INSTR(u2.unique_id, '#') + 1)
                   ELSE u1.name || '#' || SUBSTR(u1.unique_id, INSTR(u1.unique_id, '#') + 1)
               END AS chat_partner_unique_id,
               pc.last_updated
        FROM private_chats pc
        JOIN users u1 ON pc.user1_id = u1.id
        JOIN users u2 ON pc.user2_id = u2.id
        WHERE pc.user1_id = ? OR pc.user2_id = ?
        ORDER BY pc.last_updated DESC
    ''', (user_id, user_id, user_id))
    chats = c.fetchall()
    conn.close()
    return chats

# Group Chat functions
def create_group_chat(name, created_by_user_id):
    """Create a new group chat."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    now = datetime.now()
    try:
        c.execute('''
            INSERT INTO group_chats
            (name, created_by_user_id, created_at, last_updated)
            VALUES (?, ?, ?, ?)
        ''', (name, created_by_user_id, now, now))
        group_chat_id = c.lastrowid
        # Add the creator as a member
        add_group_member(group_chat_id, created_by_user_id)
        conn.commit()
        conn.close()
        return group_chat_id
    except sqlite3.IntegrityError as e:
        print(f"Error creating group chat: {e}")
        conn.close()
        return None

def add_group_member(group_chat_id, user_id):
    """Add a user to a group chat."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    now = datetime.now()
    try:
        c.execute('''
            INSERT INTO group_chat_members
            (group_chat_id, user_id, joined_at)
            VALUES (?, ?, ?)
        ''', (group_chat_id, user_id, now))
        conn.commit()
        conn.close()
        return True
    except sqlite3.IntegrityError as e:
        print(f"User {user_id} is already a member of group chat {group_chat_id} or other error: {e}")
        conn.close()
        return False

def remove_group_member(group_chat_id, user_id):
    """Remove a user from a group chat."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('DELETE FROM group_chat_members WHERE group_chat_id = ? AND user_id = ?',
              (group_chat_id, user_id))
    conn.commit()
    conn.close()
    return c.rowcount > 0

def get_group_members(group_chat_id):
    """Get all members of a group chat."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('''
        SELECT u.id, u.name, u.unique_id
        FROM group_chat_members gcm
        JOIN users u ON gcm.user_id = u.id
        WHERE gcm.group_chat_id = ?
    ''', (group_chat_id,))
    members = c.fetchall()
    conn.close()
    return members

def save_group_message(group_chat_id, sender_id, content, is_ai_message=False):
    """Save a message in a group chat."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    now = datetime.now()
    c.execute('INSERT INTO group_messages (group_chat_id, sender_id, content, timestamp, is_ai_message) VALUES (?, ?, ?, ?, ?)',
              (group_chat_id, sender_id, content, now, is_ai_message))
    c.execute('UPDATE group_chats SET last_updated = ? WHERE id = ?',
              (now, group_chat_id))
    conn.commit()
    conn.close()

def get_group_messages(group_chat_id):
    """Get all messages from a group chat."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('''
        SELECT gm.sender_id, u.name, u.unique_id, gm.content, gm.timestamp, gm.is_ai_message
        FROM group_messages gm
        JOIN users u ON gm.sender_id = u.id
        WHERE gm.group_chat_id = ? ORDER BY gm.timestamp
    ''', (group_chat_id,))
    messages = c.fetchall()
    conn.close()
    return messages

def get_user_group_chats(user_id):
    """Get all group chats a user is a member of."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('''
        SELECT gc.id, gc.name, gc.last_updated
        FROM group_chats gc
        JOIN group_chat_members gcm ON gc.id = gcm.group_chat_id
        WHERE gcm.user_id = ?
        ORDER BY gc.last_updated DESC
    ''', (user_id,))
    chats = c.fetchall()
    conn.close()
    return chats

def get_group_chat_by_id(group_chat_id):
    """Get group chat details by ID."""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('SELECT id, name, created_by_user_id, created_at, last_updated FROM group_chats WHERE id = ?', (group_chat_id,))
    chat = c.fetchone()
    conn.close()
    return chat
